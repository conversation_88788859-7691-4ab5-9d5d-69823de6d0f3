using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogInputDtoValidator : AbstractValidator<LogInputDto>
{
    public LogInputDtoValidator()
    {
        RuleFor(x => x.Ts)
            .NotEmpty()
            .WithMessage("Timestamp é obrigatório");

        RuleFor(x => x.Level)
            .NotEmpty()
            .WithMessage("Level é obrigatório");

        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Message é obrigatória");

        RuleFor(x => x.Tenant)
            .NotEmpty()
            .WithMessage("Tenant é obrigatório");
        
        RuleFor(x => x)
            .NotNull()
            .WithMessage("Lista de logs não pode ser nula");
    }
}
