using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogInputDtoValidator : AbstractValidator<LogInputDto>
{
    public LogInputDtoValidator()
    {
        RuleFor(x => x.Ts)
            .NotEmpty()
            .WithMessage("Timestamp é obrigatório");

        RuleFor(x => x.Level)
            .NotEmpty()
            .WithMessage("Level é obrigatório")
            .Must(BeValidLogLevel)
            .WithMessage("Level inválido. Valores válidos: Trace, Debug, Information, Warning, Error, Critical");

        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Message é obrigatória")
            .MaximumLength(5000)
            .WithMessage("Message muito longa (máximo 5000 caracteres)");

        RuleFor(x => x.Tenant)
            .NotEmpty()
            .WithMessage("Tenant é obrigatório")
            .MaximumLength(50)
            .WithMessage("Tenant muito longo (máximo 50 caracteres)");

        RuleFor(x => x.Exception!.Message)
            .MaximumLength(2000)
            .WithMessage("Exception Message muito longa (máximo 2000 caracteres)")
            .When(x => x.Exception != null && !string.IsNullOrWhiteSpace(x.Exception.Message));
    }
    
    public class LogBatchValidator : AbstractValidator<IEnumerable<LogInputDto>>
    {
        public LogBatchValidator()
        {
            RuleFor(x => x)
                .NotNull()
                .WithMessage("Lista de logs não pode ser nula");

            RuleFor(x => x)
                .Must(HaveAtLeastOneLog)
                .WithMessage("Nenhum log fornecido")
                .When(x => x != null);

            RuleForEach(x => x)
                .SetValidator(new LogInputDtoValidator())
                .When(x => x != null);
        }

        private static bool HaveAtLeastOneLog(IEnumerable<LogInputDto> logs)
        {
            return logs.Any();
        }
    }

    private static bool BeValidLogLevel(string? level)
    {
        if (string.IsNullOrWhiteSpace(level)) return false;

        var validLevels = new[] { "Trace", "Debug", "Information", "Warning", "Error", "Critical" };
        return validLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }

    private static bool BeValidGuid(string? correlationId)
    {
        return string.IsNullOrWhiteSpace(correlationId) || Guid.TryParse(correlationId, out _);
    }
}
