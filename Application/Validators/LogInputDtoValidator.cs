using FluentValidation;
using Zin.Logging.Shared.DTOs;

namespace Zin.Logging.Application.Validators;

public class LogInputDtoValidator : AbstractValidator<LogInputDto>
{
    private static readonly string[] ValidLogLevels =
    {
        "Trace", "Debug", "Information", "Warning", "Error", "Critical"
    };

    public LogInputDtoValidator()
    {
        ConfigureTimestampValidation();
        ConfigureLevelValidation();
        ConfigureMessageValidation();
        ConfigureTenantValidation();
        ConfigureExceptionValidation();
    }

    private void ConfigureTimestampValidation()
    {
        RuleFor(x => x.Ts)
            .NotEmpty()
            .WithMessage("Timestamp é obrigatório");
    }

    private void ConfigureLevelValidation()
    {
        RuleFor(x => x.Level)
            .NotEmpty()
            .WithMessage("Level é obrigatório")
            .Must(BeValidLogLevel)
            .WithMessage($"Level inválido. Valores válidos: {string.Join(", ", ValidLogLevels)}");
    }

    private void ConfigureMessageValidation()
    {
        RuleFor(x => x.Message)
            .NotEmpty()
            .WithMessage("Message é obrigatória")
            .MaximumLength(5000)
            .WithMessage("Message muito longa (máximo 5000 caracteres)");
    }

    private void ConfigureTenantValidation()
    {
        RuleFor(x => x.Tenant)
            .NotEmpty()
            .WithMessage("Tenant é obrigatório")
            .MaximumLength(50)
            .WithMessage("Tenant muito longo (máximo 50 caracteres)");
    }

    private void ConfigureExceptionValidation()
    {
        RuleFor(x => x.Exception!.Message)
            .MaximumLength(2000)
            .WithMessage("Exception Message muito longa (máximo 2000 caracteres)")
            .When(x => x.Exception != null && !string.IsNullOrWhiteSpace(x.Exception.Message));
    }

    private static bool BeValidLogLevel(string? level)
    {
        return !string.IsNullOrWhiteSpace(level) &&
               ValidLogLevels.Contains(level, StringComparer.OrdinalIgnoreCase);
    }
}

public class LogBatchValidator : AbstractValidator<IEnumerable<LogInputDto>>
{
    public LogBatchValidator()
    {
        ConfigureBatchValidation();
        ConfigureIndividualLogValidation();
    }

    private void ConfigureBatchValidation()
    {
        RuleFor(x => x)
            .NotNull()
            .WithMessage("Lista de logs não pode ser nula")
            .Must(HaveAtLeastOneLog)
            .WithMessage("Nenhum log fornecido")
            .When(x => x != null);
    }

    private void ConfigureIndividualLogValidation()
    {
        RuleForEach(x => x)
            .SetValidator(new LogInputDtoValidator())
            .When(x => x != null);
    }

    private static bool HaveAtLeastOneLog(IEnumerable<LogInputDto> logs)
    {
        return logs.Any();
    }
}
